.evaluation-form-container {
  min-height: 100vh;
  background: #f5f5f5;

  // 固定头部样式
  .fixed-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.95);
    border-bottom: 1px solid #e8e8e8;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;

    // 滚动时增强阴影效果
    &.scrolled {
      background: rgba(255, 255, 255, 0.98);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      border-bottom-color: #d9d9d9;
    }

    .header-content {
      max-width: 900px;
      margin: 0 auto;
      padding: 12px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 16px;

      .header-left {
        flex-shrink: 0;

        .back-button {
          color: #1890ff;
          font-weight: 500;
          padding: 4px 8px;
          height: auto;

          &:hover {
            color: #40a9ff;
            background: rgba(24, 144, 255, 0.1);
          }
        }
      }

      .header-center {
        flex: 1;
        text-align: center;
        min-width: 0;

        .header-title {
          margin: 0 !important;
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .header-meta {
          margin-top: 4px;
          display: flex;
          justify-content: center;
          gap: 16px;
          flex-wrap: wrap;

          .ant-typography {
            font-size: 12px;
            margin: 0;
          }
        }
      }

      .header-right {
        flex-shrink: 0;

        .progress-info {
          display: flex;
          align-items: center;
          gap: 8px;

          .ant-progress {
            width: 80px;
            margin: 0;

            .ant-progress-bg {
              height: 6px !important;
            }

            .ant-progress-inner {
              .ant-progress-bg {
                height: 6px !important;
              }
            }
          }

          .progress-text {
            font-size: 12px;
            font-weight: 500;
            color: #1890ff;
            min-width: 32px;
          }
        }
      }
    }
  }

  // 主要内容区域
  .evaluation-form-content {
    max-width: 900px;
    margin: 0 auto;
    padding: 80px 20px 20px; // 顶部留出固定头部的空间

    .form-card {
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
      border: none;

      .ant-card-body {
        padding: 32px;
      }
    }

    .school-rating-section {
      margin-bottom: 32px;

      .rating-card {
        border: 2px solid #e6f7ff;
        border-radius: 12px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);

        .ant-card-body {
          padding: 24px;
        }

        .rating-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 20px;

          .section-icon {
            font-size: 20px;
            color: #1890ff;
          }

          .ant-typography {
            margin: 0 !important;
            color: #1890ff;
          }
        }

        .rating-content {
          .rating-input {
            margin-bottom: 20px;
            text-align: center;

            .ant-typography {
              display: block;
              margin-bottom: 12px;
            }

            .ant-rate {
              margin: 0 12px;
            }
          }

          .comment-input {
            .ant-typography {
              display: block;
              margin-bottom: 8px;
            }
          }
        }
      }
    }

    .teacher-rating-section {
      margin-bottom: 32px;

      .section-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;

        .section-icon {
          font-size: 20px;
          color: #52c41a;
        }

        .ant-typography {
          margin: 0 !important;
        }
      }

      .teacher-card {
        border-radius: 12px;
        border: 2px solid #f0f0f0;
        transition: all 0.3s;

        &.rated {
          border-color: #52c41a;
          background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
        }

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 10%);
        }

        .ant-card-body {
          padding: 20px;
        }

        .teacher-info {
          .teacher-basic {
            margin-bottom: 16px;

            .ant-typography {
              margin-bottom: 8px !important;
            }
          }

          .teacher-rating {
            .rating-input {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 16px;
              flex-wrap: wrap;

              .ant-typography {
                margin: 0;
                min-width: 40px;
              }

              .ant-rate {
                margin: 0;
              }
            }

            .comment-input {
              .ant-typography {
                display: block;
                margin-bottom: 8px;
              }
            }
          }
        }
      }
    }

    .submit-section {
      text-align: center;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;

      .submit-button {
        height: 48px;
        padding: 0 32px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        background: linear-gradient(135deg, #52c41a, #389e0d);
        border: none;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 30%);

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #73d13d, #52c41a);
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(82, 196, 26, 40%);
        }

        &:disabled {
          background: #d9d9d9;
          box-shadow: none;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .evaluation-form-container {
    .fixed-header {
      .header-content {
        padding: 8px 16px;
        gap: 8px;

        .header-center {
          .header-title {
            font-size: 14px;
          }

          .header-meta {
            gap: 8px;

            .ant-typography {
              font-size: 11px;
            }
          }
        }

        .header-right {
          .progress-info {
            .ant-progress {
              width: 60px;
            }

            .progress-text {
              font-size: 11px;
              min-width: 28px;
            }
          }
        }
      }
    }

    .evaluation-form-content {
      padding: 70px 16px 16px; // 移动端减少顶部间距

      .form-card {
        .ant-card-body {
          padding: 20px;
        }
      }

      .school-rating-section {
        .rating-card {
          .rating-content {
            .rating-input {
              .ant-rate {
                font-size: 20px;
              }
            }
          }
        }
      }

      .teacher-rating-section {
        .teacher-card {
          .teacher-info {
            .teacher-rating {
              .rating-input {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .ant-rate {
                  font-size: 18px;
                }
              }
            }
          }
        }
      }

      .submit-section {
        .submit-button {
          width: 100%;
          height: 44px;
          font-size: 14px;
        }
      }
    }
  }
}
