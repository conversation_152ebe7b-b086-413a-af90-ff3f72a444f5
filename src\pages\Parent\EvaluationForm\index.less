.evaluation-form-container {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;

  .evaluation-form-content {
    max-width: 900px;
    margin: 0 auto;

    .form-card {
      border-radius: 16px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 8%);
      border: none;

      .ant-card-body {
        padding: 32px;
      }
    }

    .form-header {
      margin-bottom: 24px;

      .back-button {
        margin-bottom: 16px;
        color: #1890ff;
        font-weight: 500;

        &:hover {
          color: #40a9ff;
          background: rgba(24, 144, 255, 10%);
        }
      }

      .header-info {
        text-align: center;
        margin-bottom: 20px;

        .ant-typography {
          margin-bottom: 8px !important;
        }

        .ant-space {
          justify-content: center;
        }
      }

      .progress-info {
        text-align: center;

        .ant-progress {
          margin-bottom: 8px;
        }
      }
    }

    .school-rating-section {
      margin-bottom: 32px;

      .rating-card {
        border: 2px solid #e6f7ff;
        border-radius: 12px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);

        .ant-card-body {
          padding: 24px;
        }

        .rating-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 20px;

          .section-icon {
            font-size: 20px;
            color: #1890ff;
          }

          .ant-typography {
            margin: 0 !important;
            color: #1890ff;
          }
        }

        .rating-content {
          .rating-input {
            margin-bottom: 20px;
            text-align: center;

            .ant-typography {
              display: block;
              margin-bottom: 12px;
            }

            .ant-rate {
              margin: 0 12px;
            }
          }

          .comment-input {
            .ant-typography {
              display: block;
              margin-bottom: 8px;
            }
          }
        }
      }
    }

    .teacher-rating-section {
      margin-bottom: 32px;

      .section-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;

        .section-icon {
          font-size: 20px;
          color: #52c41a;
        }

        .ant-typography {
          margin: 0 !important;
        }
      }

      .teacher-card {
        border-radius: 12px;
        border: 2px solid #f0f0f0;
        transition: all 0.3s;

        &.rated {
          border-color: #52c41a;
          background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
        }

        &:hover {
          box-shadow: 0 4px 16px rgba(0, 0, 0, 10%);
        }

        .ant-card-body {
          padding: 20px;
        }

        .teacher-info {
          .teacher-basic {
            margin-bottom: 16px;

            .ant-typography {
              margin-bottom: 8px !important;
            }
          }

          .teacher-rating {
            .rating-input {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 16px;
              flex-wrap: wrap;

              .ant-typography {
                margin: 0;
                min-width: 40px;
              }

              .ant-rate {
                margin: 0;
              }
            }

            .comment-input {
              .ant-typography {
                display: block;
                margin-bottom: 8px;
              }
            }
          }
        }
      }
    }

    .submit-section {
      text-align: center;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;

      .submit-button {
        height: 48px;
        padding: 0 32px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        background: linear-gradient(135deg, #52c41a, #389e0d);
        border: none;
        box-shadow: 0 4px 12px rgba(82, 196, 26, 30%);

        &:hover:not(:disabled) {
          background: linear-gradient(135deg, #73d13d, #52c41a);
          transform: translateY(-1px);
          box-shadow: 0 6px 16px rgba(82, 196, 26, 40%);
        }

        &:disabled {
          background: #d9d9d9;
          box-shadow: none;
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .evaluation-form-container {
    padding: 16px;

    .evaluation-form-content {
      .form-card {
        .ant-card-body {
          padding: 20px;
        }
      }

      .form-header {
        .header-info {
          .ant-space {
            flex-direction: column;
            gap: 4px;
          }
        }
      }

      .school-rating-section {
        .rating-card {
          .rating-content {
            .rating-input {
              .ant-rate {
                font-size: 20px;
              }
            }
          }
        }
      }

      .teacher-rating-section {
        .teacher-card {
          .teacher-info {
            .teacher-rating {
              .rating-input {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;

                .ant-rate {
                  font-size: 18px;
                }
              }
            }
          }
        }
      }

      .submit-section {
        .submit-button {
          width: 100%;
          height: 44px;
          font-size: 14px;
        }
      }
    }
  }
}
