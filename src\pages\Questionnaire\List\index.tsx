import {
  DeleteOutlined,
  EditOutlined,
  MoreOutlined,
  PlayCircleOutlined,
  PlusOutlined,
  StopOutlined,
} from '@ant-design/icons';
import type { ActionType, ProColumns } from '@ant-design/pro-components';
import { ProTable } from '@ant-design/pro-components';
import { history, useModel } from '@umijs/max';
import type { MenuProps } from 'antd';
import { Button, Dropdown, Modal, Space, Tag } from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import './index.less';

const { confirm } = Modal;

/**
 * 问卷列表页面
 */
const QuestionnaireList: React.FC = () => {
  const {
    fetchQuestionnaireList,
    updateQuestionnaireStatusAction,
    deleteQuestionnaireAction,
  } = useModel('questionnaire');

  const actionRef = useRef<ActionType>();
  const [isMobile, setIsMobile] = useState(false);

  // 检测屏幕尺寸
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkScreenSize();
    window.addEventListener('resize', checkScreenSize);

    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  // 搜索处理
  const handleSearch = async (params: any) => {
    const queryParams: API.IQuestionnaireQuery = {
      page: params.current || 1,
      limit: params.pageSize || 10,
      month: params.month,
      status: params.status,
    };

    const result = await fetchQuestionnaireList(queryParams);
    return {
      data: result?.list || [],
      total: result?.total || 0,
      success: true,
    };
  };

  // 状态标签渲染
  const renderStatusTag = (status: string) => {
    const statusMap = {
      draft: { color: 'default', text: '草稿' },
      published: { color: 'success', text: '发布' },
      closed: { color: 'error', text: '关闭' },
    };
    const config = statusMap[status as keyof typeof statusMap] || {
      color: 'default',
      text: status,
    };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 状态变更
  const handleStatusChange = (
    id: number,
    status: string,
    currentStatus?: string,
  ) => {
    let statusText = '';
    let confirmContent = '';

    if (status === 'published') {
      if (currentStatus === 'closed') {
        statusText = '重新发布';
        confirmContent = '确定要重新发布这个问卷吗？重新发布后问卷将重新开放。';
      } else {
        statusText = '发布';
        confirmContent = '确定要发布这个问卷吗？';
      }
    } else if (status === 'closed') {
      statusText = '关闭';
      confirmContent = '确定要关闭这个问卷吗？关闭后可以重新发布。';
    }

    confirm({
      title: `确认${statusText}问卷？`,
      content: confirmContent,
      onOk: async () => {
        const result = await updateQuestionnaireStatusAction(id, { status });
        if (result && actionRef.current) {
          actionRef.current.reload();
        }
      },
    });
  };

  // 删除问卷
  const handleDelete = (id: number) => {
    confirm({
      title: '确认删除？',
      content: '删除后无法恢复，确定要删除这个问卷吗？',
      okType: 'danger',
      onOk: async () => {
        const result = await deleteQuestionnaireAction(id);
        if (result && actionRef.current) {
          actionRef.current.reload();
        }
      },
    });
  };

  // 获取操作菜单项
  const getActionMenuItems = (record: any): MenuProps['items'] => {
    const items: MenuProps['items'] = [
      {
        key: 'edit',
        label: '编辑',
        icon: <EditOutlined />,
        onClick: () => history.push(`/questionnaire/edit/${record.id}`),
      },
    ];

    if (record.status === 'draft') {
      items.push({
        key: 'publish',
        label: '发布',
        icon: <PlayCircleOutlined />,
        onClick: () =>
          handleStatusChange(record.id, 'published', record.status),
      });
    }

    if (record.status === 'published') {
      items.push({
        key: 'close',
        label: '关闭',
        icon: <StopOutlined />,
        onClick: () => handleStatusChange(record.id, 'closed', record.status),
      });
    }

    if (record.status === 'closed') {
      items.push({
        key: 'republish',
        label: '重新发布',
        icon: <PlayCircleOutlined />,
        onClick: () =>
          handleStatusChange(record.id, 'published', record.status),
      });
    }

    if (record.status !== 'published') {
      items.push({
        type: 'divider',
      });

      items.push({
        key: 'delete',
        label: '删除',
        icon: <DeleteOutlined />,
        danger: true,
        onClick: () => handleDelete(record.id),
      });
    }

    return items;
  };

  // 操作按钮
  const renderActions = (record: any) => {
    if (isMobile) {
      // 移动端使用下拉菜单
      return (
        <Dropdown
          menu={{ items: getActionMenuItems(record) }}
          trigger={['click']}
          placement="bottomRight"
        >
          <Button
            type="link"
            size="small"
            icon={<MoreOutlined />}
            onClick={(e) => e.preventDefault()}
          >
            操作
          </Button>
        </Dropdown>
      );
    }

    // 桌面端显示所有按钮
    return (
      <Space className="table-actions">
        <Button
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => history.push(`/questionnaire/edit/${record.id}`)}
        >
          编辑
        </Button>

        {record.status === 'draft' && (
          <Button
            type="link"
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() =>
              handleStatusChange(record.id, 'published', record.status)
            }
          >
            发布
          </Button>
        )}

        {record.status === 'published' && (
          <Button
            type="link"
            size="small"
            icon={<StopOutlined />}
            onClick={() =>
              handleStatusChange(record.id, 'closed', record.status)
            }
          >
            关闭
          </Button>
        )}

        {record.status === 'closed' && (
          <Button
            type="link"
            size="small"
            icon={<PlayCircleOutlined />}
            onClick={() =>
              handleStatusChange(record.id, 'published', record.status)
            }
          >
            重新发布
          </Button>
        )}

        {record.status !== 'published' && (
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        )}
      </Space>
    );
  };

  // 表格列定义
  const columns: ProColumns<any>[] = [
    {
      title: '问卷标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      search: false,
      // 移动端时标题列占更多空间
      width: isMobile ? undefined : 'auto',
      render: (_, record) => {
        if (isMobile) {
          // 移动端显示标题和额外信息
          return (
            <div className="mobile-title-cell">
              <div className="title">{record.title}</div>
              <div className="meta-info">
                <span className="month">
                  {dayjs(record.month).format('YYYY-MM')}
                </span>
                <span className="time">
                  {dayjs(record.created_at).format('MM-DD HH:mm')}
                </span>
              </div>
            </div>
          );
        }
        return record.title;
      },
    },
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
      width: isMobile ? 80 : 120,
      valueType: 'dateMonth',
      render: (_, record) =>
        dayjs(record.month).format(isMobile ? 'MM月' : 'YYYY-MM'),
      // 移动端隐藏月份列，在标题下方显示
      hideInTable: isMobile,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: isMobile ? 60 : 100,
      valueType: 'select',
      valueEnum: {
        draft: { text: '草稿', status: 'Default' },
        published: { text: '发布', status: 'Success' },
        closed: { text: '关闭', status: 'Error' },
      },
      render: (_, record) => renderStatusTag(record.status),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: isMobile ? 0 : 180,
      search: false,
      sorter: true,
      // 移动端隐藏创建时间列
      hideInTable: isMobile,
      render: (_, record) =>
        dayjs(record.created_at).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: isMobile ? 80 : 220,
      search: false,
      fixed: isMobile ? 'right' : false,
      render: (_, record) => renderActions(record),
    },
  ];

  return (
    <div className="questionnaire-list">
      <ProTable<any>
        actionRef={actionRef}
        columns={columns}
        request={handleSearch}
        rowKey="id"
        search={{
          labelWidth: 'auto',
          // 移动端收起搜索表单
          collapsed: isMobile,
          collapseRender: isMobile ? false : undefined,
        }}
        toolBarRender={() => [
          <Button
            key="create"
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => history.push('/questionnaire/create')}
            size={isMobile ? 'middle' : 'middle'}
          >
            {isMobile ? '创建' : '创建问卷'}
          </Button>,
        ]}
        pagination={{
          showSizeChanger: !isMobile,
          showQuickJumper: !isMobile,
          showTotal: (total) => `共 ${total} 条记录`,
          pageSize: isMobile ? 10 : 20,
          simple: isMobile,
        }}
        options={{
          reload: true,
          density: !isMobile,
          setting: !isMobile,
        }}
        scroll={{
          x: isMobile ? 'max-content' : undefined,
        }}
        size={isMobile ? 'small' : 'middle'}
      />
    </div>
  );
};

export default QuestionnaireList;
