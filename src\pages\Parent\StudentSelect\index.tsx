import {
  ArrowLeftOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  FilterOutlined,
  FormOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useModel } from '@umijs/max';
import {
  Alert,
  Avatar,
  Badge,
  Button,
  Card,
  Select,
  Space,
  Tag,
  Typography,
} from 'antd';
import React, { useMemo } from 'react';
import './index.less';

const { Title, Text } = Typography;

/**
 * 学生选择页面
 */
const StudentSelect: React.FC = () => {
  const {
    loading,
    parentInfo,
    studentList,
    studentQuestionnaireStatus,
    schoolCodeFilter,
    selectStudent,
    setSchoolFilter,
    goBack,
  } = useModel('parent');

  // 获取学校列表
  const schoolOptions = useMemo(() => {
    const schools = new Map();
    studentList.forEach((student) => {
      if (!schools.has(student.school_code)) {
        schools.set(student.school_code, {
          code: student.school_code,
          name: `学校 ${student.school_code}`, // 这里可以从studentQuestionnaireStatus获取真实学校名称
        });
      }
    });

    // 从问卷状态中获取真实学校名称
    studentQuestionnaireStatus.forEach((status) => {
      if (schools.has(status.school_code)) {
        schools.set(status.school_code, {
          code: status.school_code,
          name: status.school_name,
        });
      }
    });

    return Array.from(schools.values());
  }, [studentList, studentQuestionnaireStatus]);

  // 筛选后的学生列表
  const filteredStudentList = useMemo(() => {
    if (!schoolCodeFilter) return studentList;
    return studentList.filter(
      (student) => student.school_code === schoolCodeFilter,
    );
  }, [studentList, schoolCodeFilter]);

  // 获取学生的问卷状态
  const getStudentQuestionnaireStatus = (studentId: string) => {
    const studentStatus = studentQuestionnaireStatus.find(
      (status) => status.student_id === studentId,
    );

    // 如果没有找到学生状态或问卷列表为空，显示"暂无问卷"
    if (!studentStatus || studentStatus.questionnaires.length === 0) {
      return {
        type: 'none' as const,
        label: '暂无问卷',
        icon: <ExclamationCircleOutlined />,
        color: 'default',
        buttonText: '暂无问卷',
        disabled: true,
        questionnaires: [],
      };
    }

    // 检查是否有已提交的问卷
    const submittedQuestionnaires = studentStatus.questionnaires.filter(
      (q) => q.is_submitted,
    );
    // 检查是否有可填写的问卷（已发布且在有效期内且未提交）
    const availableQuestionnaires = studentStatus.questionnaires.filter(
      (q) => q.is_available && !q.is_submitted,
    );
    // 检查是否有不可填写的问卷（未发布或已过期但未提交）
    const unavailableQuestionnaires = studentStatus.questionnaires.filter(
      (q) => !q.is_available && !q.is_submitted,
    );

    // 优先级：已填写 > 待填写 > 暂不可填
    if (submittedQuestionnaires.length > 0) {
      // 有已提交的问卷
      if (availableQuestionnaires.length > 0) {
        // 还有可填写的问卷
        return {
          type: 'submitted' as const,
          label: '已填写',
          icon: <CheckCircleOutlined />,
          color: 'success',
          buttonText: '查看/填写',
          disabled: false,
          questionnaires: studentStatus.questionnaires,
          summary: `已填写${submittedQuestionnaires.length}个，待填写${availableQuestionnaires.length}个`,
        };
      } else {
        // 只有已提交的问卷，没有可填写的
        return {
          type: 'submitted' as const,
          label: '已填写',
          icon: <CheckCircleOutlined />,
          color: 'success',
          buttonText: '查看问卷',
          disabled: false,
          questionnaires: studentStatus.questionnaires,
          summary: `已填写${submittedQuestionnaires.length}个问卷`,
        };
      }
    }

    if (availableQuestionnaires.length > 0) {
      // 有可填写的问卷
      return {
        type: 'available' as const,
        label: '待填写',
        icon: <ClockCircleOutlined />,
        color: 'processing',
        buttonText: '填写问卷',
        disabled: false,
        questionnaires: studentStatus.questionnaires,
        summary: `${availableQuestionnaires.length}个问卷待填写`,
      };
    }

    if (unavailableQuestionnaires.length > 0) {
      // 只有不可填写的问卷
      return {
        type: 'unavailable' as const,
        label: '暂不可填',
        icon: <ClockCircleOutlined />,
        color: 'default',
        buttonText: '暂不可填',
        disabled: true,
        questionnaires: studentStatus.questionnaires,
        summary: `${unavailableQuestionnaires.length}个问卷暂不可填`,
      };
    }

    // 理论上不应该到达这里，但作为兜底
    return {
      type: 'none' as const,
      label: '暂无问卷',
      icon: <ExclamationCircleOutlined />,
      color: 'default',
      buttonText: '暂无问卷',
      disabled: true,
      questionnaires: [],
    };
  };

  // 处理学生选择
  const handleSelectStudent = async (student: API.ISSoStudentInfo) => {
    console.log('=== handleSelectStudent called ===');
    console.log('Student:', student.name, student.id);
    console.log('Loading state:', loading);
    console.log('studentQuestionnaireStatus length:', studentQuestionnaireStatus.length);
    console.log('studentQuestionnaireStatus:', studentQuestionnaireStatus);

    const questionnaireStatus = getStudentQuestionnaireStatus(student.id);
    console.log('Questionnaire status:', questionnaireStatus);

    if (questionnaireStatus.disabled) {
      console.log('Student disabled, returning early');
      return; // 无问卷或不可填写时不执行任何操作
    }

    console.log('Calling selectStudent from parent model...');
    try {
      const result = await selectStudent(student);
      console.log('selectStudent result:', result);
    } catch (error) {
      console.error('selectStudent error:', error);
    }
  };

  // 处理学校筛选
  const handleSchoolFilterChange = (schoolCode: string) => {
    setSchoolFilter(schoolCode);
  };

  // 获取年级颜色
  const getGradeColor = (grade: string) => {
    const gradeColors: Record<string, string> = {
      一年级: 'red',
      二年级: 'orange',
      三年级: 'gold',
      四年级: 'green',
      五年级: 'blue',
      六年级: 'purple',
    };
    return gradeColors[grade] || 'default';
  };

  return (
    <div className="student-select-container">
      <div className="student-select-content">
        <Card className="select-card">
          <div className="card-header">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={goBack}
              className="back-button"
            >
              返回
            </Button>

            <div className="header-info">
              <Title level={3} className="header-title">
                选择学生
              </Title>
              <Text type="secondary" className="header-subtitle">
                {parentInfo?.name && `${parentInfo.name}，`}请选择要评价的学生
              </Text>
            </div>

            {/* 学校筛选 */}
            {schoolOptions.length > 1 && (
              <div className="school-filter">
                <Space>
                  <FilterOutlined />
                  <Text>学校筛选：</Text>
                  <Select
                    style={{ width: 200 }}
                    placeholder="选择学校"
                    allowClear
                    value={schoolCodeFilter || undefined}
                    onChange={handleSchoolFilterChange}
                    options={[
                      { label: '全部学校', value: '' },
                      ...schoolOptions.map((school) => ({
                        label: school.name,
                        value: school.code,
                      })),
                    ]}
                  />
                </Space>
              </div>
            )}
          </div>

          {/* 学生列表 */}
          <div className="student-list">
            {filteredStudentList.length === 0 ? (
              <Alert
                message="暂无关联学生"
                description={
                  schoolCodeFilter
                    ? '该学校暂无关联学生信息'
                    : '您的手机号暂未关联任何学生信息，请联系学校确认'
                }
                type="warning"
                showIcon
              />
            ) : (
              <Space
                direction="vertical"
                size="large"
                style={{ width: '100%' }}
              >
                {filteredStudentList.map((student) => {
                  const questionnaireStatus = getStudentQuestionnaireStatus(
                    student.id,
                  );

                  return (
                    <Card
                      key={student.id}
                      className={`student-card ${
                        questionnaireStatus.disabled ? 'disabled' : 'available'
                      }`}
                      hoverable={!questionnaireStatus.disabled}
                    >
                      <div className="student-info">
                        <div className="student-avatar">
                          <Badge
                            count={
                              questionnaireStatus.questionnaires?.length || 0
                            }
                            color={questionnaireStatus.color}
                            offset={[-8, 8]}
                          >
                            <Avatar size={64} icon={<UserOutlined />} />
                          </Badge>
                        </div>

                        <div className="student-details">
                          <div className="student-name">
                            <Title level={4}>{student.name}</Title>
                            <Tag color={getGradeColor(student.grade)}>
                              {student.grade}
                            </Tag>
                          </div>

                          <div className="student-class">
                            <Text type="secondary">班级：{student.class}</Text>
                          </div>

                          <div className="student-status">
                            <Space>
                              <Tag
                                color={
                                  student.status === 'active'
                                    ? 'success'
                                    : 'default'
                                }
                              >
                                {student.status === 'active' ? '在读' : '其他'}
                              </Tag>

                              <Tag
                                color={questionnaireStatus.color}
                                icon={questionnaireStatus.icon}
                              >
                                {questionnaireStatus.label}
                              </Tag>
                            </Space>
                          </div>

                          {/* 问卷详情 */}
                          {questionnaireStatus.questionnaires &&
                            questionnaireStatus.questionnaires.length > 0 && (
                              <div className="questionnaire-summary">
                                <Text
                                  type="secondary"
                                  style={{ fontSize: '12px' }}
                                >
                                  {questionnaireStatus.summary ||
                                    (questionnaireStatus.questionnaires.length >
                                    1
                                      ? `${questionnaireStatus.questionnaires.length}个问卷`
                                      : questionnaireStatus.questionnaires[0]
                                          .title)}
                                </Text>
                              </div>
                            )}
                        </div>

                        <div className="student-action">
                          <Button
                            type={
                              questionnaireStatus.type === 'submitted'
                                ? 'default'
                                : 'primary'
                            }
                            icon={
                              questionnaireStatus.type === 'submitted' ? (
                                <EyeOutlined />
                              ) : (
                                <FormOutlined />
                              )
                            }
                            loading={loading}
                            size="large"
                            disabled={questionnaireStatus.disabled}
                            onClick={() => handleSelectStudent(student)}
                          >
                            {questionnaireStatus.buttonText}
                          </Button>
                        </div>
                      </div>
                    </Card>
                  );
                })}
              </Space>
            )}
          </div>

          <div className="select-tips">
            <Alert
              message="评价说明"
              description={
                <div>
                  <p>
                    • <Tag color="success">已填写</Tag>{' '}
                    表示已完成部分问卷，可查看已填写内容或继续填写其他问卷
                  </p>
                  <p>
                    • <Tag color="processing">待填写</Tag> 表示有问卷可以填写
                  </p>
                  <p>
                    • <Tag color="default">暂无问卷</Tag>{' '}
                    表示暂时没有可填写的问卷
                  </p>
                  <p>
                    • <Tag color="default">暂不可填</Tag> 表示问卷未开放或已截止
                  </p>
                  <p>• 点击学生卡片进入对应的问卷页面</p>
                </div>
              }
              type="info"
              showIcon
            />
          </div>
        </Card>
      </div>
    </div>
  );
};

export default StudentSelect;
