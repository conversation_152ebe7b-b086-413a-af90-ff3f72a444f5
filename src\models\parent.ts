import {
  getStudentQuestionnaires,
  getTeacherListForClassParams,
  submitParentEvaluation,
  verifyParentPhone,
} from '@/services';
import { handleApiResponse, handleException } from '@/utils/errorHandler';
import { message } from 'antd';
import _ from 'lodash';
import { useCallback, useState } from 'react';

/**
 * 家长端数据模型
 * @description 管理家长端问卷填写流程的状态和数据
 */
export default function useParentModel() {
  // 基础状态
  const [loading, setLoading] = useState(false);
  const [currentStep, setCurrentStep] = useState<
    'phone' | 'student' | 'questionnaire' | 'evaluation'
  >('phone');

  // 家长信息
  const [parentPhone, setParentPhone] = useState('');
  const [parentInfo, setParentInfo] = useState<any>(null);

  // 学生信息
  const [studentList, setStudentList] = useState<API.ISSoStudentInfo[]>([]);
  const [selectedStudent, setSelectedStudent] =
    useState<API.ISSoStudentInfo | null>(null);
  const [studentQuestionnaireStatus, setStudentQuestionnaireStatus] = useState<
    API.IStudentQuestionnaireStatus[]
  >([]);
  const [schoolCodeFilter, setSchoolCodeFilter] = useState<string>('');

  // 问卷信息
  const [questionnaireList, setQuestionnaireList] = useState<
    API.IParentQuestionnaireInfo[]
  >([]);
  const [selectedQuestionnaire, setSelectedQuestionnaire] =
    useState<API.IParentQuestionnaireInfo | null>(null);
  const [teacherList, setTeacherList] = useState<API.IStudentTeacherInfo[]>([]);

  // 评价数据
  const [evaluationData, setEvaluationData] = useState<API.IEvaluationData>({
    school_rating: 0,
    school_comment: '',
    teacher_evaluations: [],
  });

  // 本地缓存键名
  const getCacheKey = (suffix: string) => {
    return `parent_evaluation_${parentPhone}_${selectedStudent?.id}_${suffix}`;
  };

  // 获取学生问卷状态
  const fetchStudentsQuestionnaireStatus = async (
    students: API.ISSoStudentInfo[],
    phone: string,
  ) => {
    if (!phone || students.length === 0) return;

    setLoading(true);
    try {
      // 为每个学生获取问卷状态
      const statusPromises = students.map(async (student) => {
        try {
          const response = await getStudentQuestionnaires({
            sso_school_code: student.school_code,
            sso_student_code: student.id,
            parent_phone: phone,
          });

          const result = handleApiResponse(response);

          // 无论API调用是否成功，都要为学生创建状态记录
          if (
            result.success &&
            result.data &&
            result.data.has_questionnaire &&
            result.data.questionnaire
          ) {
            // API调用成功，有问卷数据
            const q = result.data.questionnaire;
            return {
              student_id: student.id,
              student_name: student.name,
              student_class: student.class,
              student_grade: student.grade,
              school_code: student.school_code,
              school_name: q.sso_school_name || `学校 ${student.school_code}`,
              questionnaires: [
                {
                  questionnaire_id: q.id,
                  title: q.title,
                  description: q.description,
                  month: q.month,
                  status: 'published', // 假设返回的问卷都是已发布状态
                  star_mode: q.star_mode,
                  include_school_evaluation: q.include_school_evaluation,
                  is_submitted: q.is_submitted,
                  submitted_at: undefined, // 后台接口暂未返回提交时间
                  start_time: q.start_time,
                  end_time: q.end_time,
                  // 判断是否可填写（基于时间和提交状态）
                  is_available:
                    !q.is_submitted &&
                    (!q.start_time || new Date() >= new Date(q.start_time)) &&
                    (!q.end_time || new Date() <= new Date(q.end_time)),
                },
              ],
            } as API.IStudentQuestionnaireStatus;
          } else {
            // API调用失败或无问卷数据，创建空状态记录
            console.log(`学生 ${student.name} 暂无问卷数据`);
            return {
              student_id: student.id,
              student_name: student.name,
              student_class: student.class,
              student_grade: student.grade,
              school_code: student.school_code,
              school_name: `学校 ${student.school_code}`,
              questionnaires: [], // 空问卷列表表示暂无问卷
            } as API.IStudentQuestionnaireStatus;
          }
        } catch (error) {
          console.warn(`获取学生 ${student.id} 问卷状态失败:`, error);

          // 即使出错也要为学生创建状态记录
          return {
            student_id: student.id,
            student_name: student.name,
            student_class: student.class,
            student_grade: student.grade,
            school_code: student.school_code,
            school_name: `学校 ${student.school_code}`,
            questionnaires: [], // 空问卷列表表示暂无问卷
          } as API.IStudentQuestionnaireStatus;
        }
      });

      const results = await Promise.all(statusPromises);
      const validResults = results.filter(
        (result) => result !== null,
      ) as API.IStudentQuestionnaireStatus[];

      setStudentQuestionnaireStatus(validResults);
    } catch (error) {
      handleException(error, '获取问卷状态失败');
      setStudentQuestionnaireStatus([]);
    } finally {
      setLoading(false);
    }
  };

  // 设置学校筛选
  const setSchoolFilter = useCallback(
    async (schoolCode: string) => {
      setSchoolCodeFilter(schoolCode);
      if (studentList.length > 0 && parentPhone) {
        await fetchStudentsQuestionnaireStatus(studentList, parentPhone);
      }
    },
    [studentList, parentPhone],
  );

  // 验证家长手机号
  const verifyPhone = useCallback(async (phone: string) => {
    setLoading(true);
    try {
      const response = await verifyParentPhone({ phone });
      const result = handleApiResponse(
        response,
        '手机号验证成功',
        '手机号验证失败，请检查是否已关联学生',
      );

      if (result.success && result.data?.is_valid) {
        const { parent } = response.data!;
        const { children, ...parentInfo } = parent!;
        setParentPhone(phone);
        setParentInfo(parentInfo);
        const students = children.map((c) => ({
          /** 学生ID */
          id: c.student.id,
          /** 学生姓名 */
          name: c.student.name,
          /** 学生学号 */
          code: c.student.work_code,
          /** 学生班级 */
          class: c.student.class.name,
          /** 学生年级 */
          grade: c.student.class.grade_name,
          /** 班级编号 */
          class_code: c.student.class.code,
          /** 年级编号 */
          grade_code: c.student.class.grade_code,
          /** 学生状态（active/inactive） */
          status: c.student.status,
          /** 所属学校代码 */
          school_code: c.student.class.enterprise.code,
        }));

        setStudentList(students);

        // 获取所有学生的问卷状态
        await fetchStudentsQuestionnaireStatus(students, phone);

        setCurrentStep('student');
        return true;
      } else {
        return false;
      }
    } catch (error) {
      handleException(error, '验证失败，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // 选择问卷
  const selectQuestionnaire = useCallback(
    async (
      questionnaire: API.IParentQuestionnaireInfo,
      student?: API.ISSoStudentInfo,
    ) => {
      setLoading(true);
      try {
        setSelectedQuestionnaire(questionnaire);

        // 使用传入的学生信息或当前选中的学生信息
        const currentStudent = student || selectedStudent;
        if (!currentStudent) {
          console.error('No student information available');
          message.error('学生信息缺失，请重新选择');
          return false;
        }

        // 获取教师列表
        const teachersResponse = await getTeacherListForClassParams({
          enterpriseCode: currentStudent.school_code,
          gradeCode: currentStudent.grade_code,
          classCode: currentStudent.class_code,
        });

        const teachersResult = handleApiResponse(teachersResponse);
        if (teachersResult.success) {
          const uniqueTeachers = _.uniqBy(
            teachersResult.data || [],
            (teacher) => `${teacher.code}_${teacher.name}`,
          ).sort((a, b) => a.courseCode.localeCompare(b.courseCode));
          setTeacherList(uniqueTeachers);

          // 如果问卷已提交，显示提示信息
          if (questionnaire.is_submitted) {
            message.info('该问卷已提交，以下为查看模式');
            // 已提交的问卷不需要从缓存恢复数据，应该显示已提交的数据
            // 这里可能需要从服务器获取已提交的评价数据
            // 暂时先清空评价数据，让组件显示为只读状态
            setEvaluationData({
              school_rating: 0,
              school_comment: '',
              teacher_evaluations: [],
            });
          } else {
            // 尝试从本地缓存恢复数据（仅对未提交的问卷）
            const cachedData = localStorage.getItem(getCacheKey('evaluation'));
            if (cachedData) {
              try {
                const parsed = JSON.parse(cachedData);
                setEvaluationData(parsed);
              } catch (e) {
                console.warn('缓存数据解析失败:', e);
              }
            }
          }

          setCurrentStep('evaluation');
          return true;
        } else {
          // API 调用失败，显示错误消息
          console.error('获取教师列表失败:', teachersResult.error);
          message.error('获取教师列表失败，请稍后重试');
          return false;
        }
      } catch (error) {
        console.error('selectQuestionnaire error:', error);
        handleException(error, '操作失败，请稍后重试');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [selectedStudent, parentPhone],
  );

  // 选择学生
  const selectStudent = useCallback(
    async (student: API.ISSoStudentInfo) => {
      setLoading(true);
      try {
        setSelectedStudent(student);

        // 从已获取的学生问卷状态中查找该学生的问卷信息
        const studentStatus = studentQuestionnaireStatus.find(
          (status) => status.student_id === student.id,
        );

        if (!studentStatus || studentStatus.questionnaires.length === 0) {
          // 没有问卷
          message.info('该学生暂无可填写的问卷');
          setQuestionnaireList([]);
          setCurrentStep('questionnaire');
          return true;
        }

        // 转换问卷数据为统一格式
        const questionnaireList: API.IParentQuestionnaireInfo[] =
          studentStatus.questionnaires.map((q) => ({
            questionnaire_id: q.questionnaire_id,
            title: q.title,
            description: q.description,
            month: q.month,
            school_info: {
              id: student.school_code,
              name: studentStatus.school_name,
            },
            class_info: {
              name: student.class,
              grade: student.grade,
            },
            star_mode: q.star_mode,
            include_school_evaluation: q.include_school_evaluation,
            status: q.status,
            is_submitted: q.is_submitted,
            submitted_at: q.submitted_at,
            start_time: q.start_time,
            end_time: q.end_time,
          }));

        setQuestionnaireList(questionnaireList);

        // 检查可填写的问卷
        const availableQuestionnaires = questionnaireList.filter(
          (q) =>
            !q.is_submitted &&
            (!q.start_time || new Date() >= new Date(q.start_time)) &&
            (!q.end_time || new Date() <= new Date(q.end_time)),
        );

        if (availableQuestionnaires.length === 0) {
          // 没有可填写的问卷，但有已提交的问卷，跳转到问卷选择页面查看
          if (questionnaireList.some((q) => q.is_submitted)) {
            message.info('该学生的问卷已提交，可查看已填写内容');
            setCurrentStep('questionnaire');
          } else {
            message.info('该学生暂无可填写的问卷');
            setCurrentStep('questionnaire');
          }
          return true;
        } else if (availableQuestionnaires.length === 1) {
          // 只有一个可填写的问卷，直接跳转到评价页面
          const result = await selectQuestionnaire(
            availableQuestionnaires[0],
            student,
          );
          return result;
        } else {
          // 有多个问卷，跳转到问卷选择页面
          setCurrentStep('questionnaire');
          return true;
        }
      } catch (error) {
        console.error('selectStudent error:', error);
        handleException(error, '操作失败，请稍后重试');
        return false;
      } finally {
        setLoading(false);
      }
    },
    [studentQuestionnaireStatus, selectQuestionnaire],
  );

  // 更新评价数据
  const updateEvaluationData = useCallback(
    (data: Partial<API.IEvaluationData>) => {
      const newData = { ...evaluationData, ...data };
      setEvaluationData(newData);

      // 保存到本地缓存
      if (selectedStudent) {
        localStorage.setItem(
          getCacheKey('evaluation'),
          JSON.stringify(newData),
        );
      }
    },
    [evaluationData, selectedStudent, parentPhone],
  );

  // 更新学校评分
  const updateSchoolRating = useCallback(
    (rating: number, comment?: string) => {
      updateEvaluationData({
        school_rating: rating,
        school_comment: comment,
      });
    },
    [updateEvaluationData, evaluationData.school_comment],
  );

  // 更新教师评分
  const updateTeacherRating = useCallback(
    (code: string, rating: number, comment?: string) => {
      const existingIndex = evaluationData.teacher_evaluations.findIndex(
        (evaluation) => evaluation.teacher_code === code,
      );

      const newEvaluation = {
        teacher_code: code,
        rating,
        comment: comment || '',
      };
      let newTeacherEvaluations;

      if (existingIndex >= 0) {
        newTeacherEvaluations = [...evaluationData.teacher_evaluations];
        newTeacherEvaluations[existingIndex] = newEvaluation;
      } else {
        newTeacherEvaluations = [
          ...evaluationData.teacher_evaluations,
          newEvaluation,
        ];
      }

      updateEvaluationData({
        teacher_evaluations: newTeacherEvaluations,
      });
    },
    [evaluationData.teacher_evaluations, updateEvaluationData],
  );

  // 检查是否所有教师都已评分
  const isAllTeachersRated = useCallback(() => {
    const availableTeachers = teacherList.filter(
      (teacher) => !teacher.is_evaluated,
    );

    // 检查每个可评价的教师是否都有评分且评分 > 0
    const unratedTeachers = availableTeachers.filter((teacher) => {
      const evaluation = evaluationData.teacher_evaluations.find(
        (e) => e.teacher_code === teacher.code,
      );
      return !evaluation || evaluation.rating === 0;
    });

    const allTeachersRated = unratedTeachers.length === 0;
    const includeSchoolEvaluation = selectedQuestionnaire?.include_school_evaluation ?? true;
    const schoolRated = includeSchoolEvaluation ? evaluationData.school_rating > 0 : true;

    return allTeachersRated && schoolRated;
  }, [teacherList, evaluationData, selectedQuestionnaire]);

  // 重置状态
  const resetState = useCallback(() => {
    setCurrentStep('phone');
    setParentPhone('');
    setParentInfo(null);
    setStudentList([]);
    setSelectedStudent(null);
    setQuestionnaireList([]);
    setSelectedQuestionnaire(null);
    setTeacherList([]);
    setStudentQuestionnaireStatus([]);
    setSchoolCodeFilter('');
    setEvaluationData({
      school_rating: 0,
      school_comment: '',
      teacher_evaluations: [],
    });
  }, []);

  // 提交评价
  const submitEvaluation = useCallback(async () => {
    if (
      !selectedStudent ||
      !selectedQuestionnaire ||
      !parentInfo ||
      !isAllTeachersRated()
    ) {
      message.warning('请完成所有评分后再提交');
      return false;
    }

    setLoading(true);
    try {
      // 转换教师评价数据格式
      const teacherEvaluations = evaluationData.teacher_evaluations.map(
        (evaluation) => {
          const teacher = teacherList.find(
            (t) => t.code === evaluation.teacher_code,
          );
          return {
            sso_teacher_id: teacher?.teacherCode || evaluation.teacher_code,
            rating: evaluation.rating,
            description: evaluation.comment || '',
          };
        },
      );

      const submitData = {
        questionnaire_id: selectedQuestionnaire.questionnaire_id,
        parent_phone: parentPhone,
        parent_name: parentInfo.name,
        sso_student_code: selectedStudent.code,
        sso_student_name: selectedStudent.name,
        month: selectedQuestionnaire.month,
        school_rating: evaluationData.school_rating,
        school_description: evaluationData.school_comment || '',
        teacher_evaluations: teacherEvaluations,
      };

      console.log('Submitting evaluation data:', submitData);

      const response = await submitParentEvaluation(submitData);

      const result = handleApiResponse(
        response,
        '提交成功！感谢您的评价',
        '提交失败，请稍后重试',
      );

      if (result.success) {
        // 清除本地缓存
        localStorage.removeItem(getCacheKey('evaluation'));

        // 重置状态
        resetState();
        return true;
      } else {
        return false;
      }
    } catch (error) {
      handleException(error, '提交失败，请稍后重试');
      return false;
    } finally {
      setLoading(false);
    }
  }, [
    selectedStudent,
    selectedQuestionnaire,
    parentPhone,
    parentInfo,
    evaluationData,
    teacherList,
    isAllTeachersRated,
  ]);

  // 返回上一步
  const goBack = useCallback(() => {
    if (currentStep === 'evaluation') {
      setCurrentStep('questionnaire');
      setSelectedQuestionnaire(null);
      setTeacherList([]);
    } else if (currentStep === 'questionnaire') {
      setCurrentStep('student');
      setQuestionnaireList([]);
      setSelectedStudent(null);
    } else if (currentStep === 'student') {
      setCurrentStep('phone');
      setStudentList([]);
      setParentInfo(null);
    }
  }, [currentStep]);

  return {
    // 状态
    loading,
    currentStep,
    parentPhone,
    parentInfo,
    studentList,
    selectedStudent,
    questionnaireList,
    selectedQuestionnaire,
    teacherList,
    evaluationData,
    studentQuestionnaireStatus,
    schoolCodeFilter,

    // 计算属性
    isAllTeachersRated: isAllTeachersRated(),

    // 方法
    verifyPhone,
    selectStudent,
    selectQuestionnaire,
    updateSchoolRating,
    updateTeacherRating,
    submitEvaluation,
    resetState,
    goBack,
    fetchStudentsQuestionnaireStatus,
    setSchoolFilter,
  };
}
